#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激光雷达网络参数自动检测工具
自动监听以太网接口，统计最频繁出现的目标IP和端口号
用于快速识别激光雷达的网络配置参数
"""

import time
import threading
from collections import defaultdict, Counter
from scapy.all import sniff, UDP, IP, Ether, Raw
import argparse
import sys
import signal


class LidarNetworkDetector:
    def __init__(self, interface=None, duration=10, min_packets=5):
        """
        初始化激光雷达网络检测器

        Args:
            interface: 网络接口名称，None表示自动选择
            duration: 监听持续时间（秒）
            min_packets: 最小数据包数量阈值
        """
        self.interface = interface
        self.duration = duration
        self.min_packets = min_packets
        self.packet_stats = defaultdict(int)  # 存储各种类型的数据包统计
        self.ethernet_stats = defaultdict(int)  # 存储以太网帧统计
        self.protocol_stats = defaultdict(int)  # 存储协议类型统计
        self.running = False
        self.start_time = None
        self.total_packets = 0
        
    def packet_handler(self, packet):
        """
        数据包处理函数 - 捕获所有类型的数据包
        """
        try:
            self.total_packets += 1

            # 分析以太网帧
            if packet.haslayer(Ether):
                src_mac = packet[Ether].src
                dst_mac = packet[Ether].dst
                eth_type = packet[Ether].type

                # 统计MAC地址对
                mac_pair = (src_mac, dst_mac)
                self.ethernet_stats[mac_pair] += 1

                # 统计协议类型
                self.protocol_stats[eth_type] += 1

            # 如果是IP数据包，也记录IP信息
            if packet.haslayer(IP):
                src_ip = packet[IP].src
                dst_ip = packet[IP].dst
                protocol = packet[IP].proto

                if packet.haslayer(UDP):
                    dst_port = packet[UDP].dport
                    key = f"UDP_{dst_ip}_{dst_port}"
                    self.packet_stats[key] += 1
                else:
                    key = f"IP_{dst_ip}_{protocol}"
                    self.packet_stats[key] += 1
            else:
                # 非IP数据包，记录以太网类型
                if packet.haslayer(Ether):
                    eth_type = packet[Ether].type
                    key = f"ETH_{hex(eth_type)}"
                    self.packet_stats[key] += 1

            # 实时显示进度
            elapsed = time.time() - self.start_time
            if int(elapsed) % 2 == 0 and elapsed > 0:  # 每2秒显示一次
                self.print_progress(elapsed)

        except Exception:
            # 忽略解析错误，继续处理其他数据包
            pass
    
    def print_progress(self, elapsed):
        """
        打印监听进度
        """
        remaining = max(0, self.duration - elapsed)
        print(f"\r监听进度: {elapsed:.0f}s/{self.duration}s, "
              f"已捕获数据包: {self.total_packets}, "
              f"剩余时间: {remaining:.0f}s", end="", flush=True)
    
    def get_network_interfaces(self):
        """
        获取可用的网络接口列表
        """
        try:
            from scapy.all import get_if_list
            interfaces = get_if_list()
            return [iface for iface in interfaces if not iface.startswith('lo')]
        except:
            return []
    
    def detect_lidar_params(self):
        """
        检测激光雷达网络参数
        """
        print("=" * 60)
        print("激光雷达网络参数自动检测工具")
        print("=" * 60)
        
        # 显示网络接口信息
        print(f"监听网络接口: {self.interface}")
        
        print(f"监听时长: {self.duration} 秒")
        print(f"最小数据包阈值: {self.min_packets}")
        print("-" * 60)
        
        # 开始监听
        print("开始监听网络数据包...")
        print("请确保激光雷达正在工作并发送数据...")
        print("-" * 60)
        
        self.running = True
        self.start_time = time.time()
        
        try:
            # 使用scapy监听数据包 - 捕获所有数据包
            sniff(
                iface=self.interface,
                prn=self.packet_handler,
                timeout=self.duration,
                store=False  # 不存储数据包，节省内存
            )
        except KeyboardInterrupt:
            print("\n\n用户中断监听...")
        except Exception as e:
            print(f"\n\n监听过程中出现错误: {e}")
        finally:
            self.running = False
        
        print("\n" + "=" * 60)
        print("监听完成，正在分析结果...")
        
        return self.analyze_results()
    
    def analyze_results(self):
        """
        分析监听结果
        """
        if self.total_packets == 0:
            print("未捕获到任何数据包！")
            print("请检查：")
            print("1. 激光雷达是否正常工作")
            print("2. 网络连接是否正常")
            print("3. 是否需要管理员权限运行此程序")
            print("4. 网络接口是否正确")
            return None

        print(f"\n总共捕获 {self.total_packets} 个数据包")

        # 显示协议类型统计
        if self.protocol_stats:
            print(f"\n📊 协议类型统计:")
            print("-" * 40)
            for eth_type, count in sorted(self.protocol_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
                percentage = (count / self.total_packets) * 100
                protocol_name = self.get_protocol_name(eth_type)
                print(f"  {protocol_name:<20} {count:>6} ({percentage:.1f}%)")

        # 显示以太网帧统计
        if self.ethernet_stats:
            print(f"\n📡 以太网通信统计 (前5名):")
            print("-" * 60)
            print(f"{'源MAC':<18} {'目标MAC':<18} {'数据包数':<8} {'占比'}")
            print("-" * 60)
            for (src_mac, dst_mac), count in sorted(self.ethernet_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
                percentage = (count / self.total_packets) * 100
                print(f"{src_mac:<18} {dst_mac:<18} {count:<8} {percentage:.1f}%")
        
        # 显示详细数据包统计
        if self.packet_stats:
            print(f"\n🔍 详细数据包分析 (前10名):")
            print("-" * 70)
            print(f"{'排名':<4} {'类型/目标':<35} {'数据包数':<10} {'占比':<8}")
            print("-" * 70)

            sorted_stats = sorted(self.packet_stats.items(), key=lambda x: x[1], reverse=True)
            likely_lidar = None

            for i, (key, count) in enumerate(sorted_stats[:10], 1):
                percentage = (count / self.total_packets) * 100
                print(f"{i:<4} {key:<35} {count:<10} {percentage:.1f}%")

                # 判断最可能的激光雷达参数
                if i == 1 and count >= self.min_packets:
                    if key.startswith("UDP_"):
                        parts = key.split("_")
                        if len(parts) >= 3:
                            dst_ip = parts[1]
                            dst_port = int(parts[2])
                            likely_lidar = (dst_ip, dst_port, count, percentage)

            print("-" * 70)

            if likely_lidar:
                dst_ip, dst_port, count, percentage = likely_lidar
                print(f"\n🎯 检测到的激光雷达参数:")
                print(f"   目标IP: {dst_ip}")
                print(f"   数据端口: {dst_port}")
                print(f"   数据包数量: {count}")
                print(f"   占比: {percentage:.1f}%")

                # 尝试推测设备端口（通常是数据端口+1）
                device_port = dst_port + 1
                print(f"   推测设备端口: {device_port} (通常为数据端口+1)")

                return {
                    'target_ip': dst_ip,
                    'data_port': dst_port,
                    'device_port': device_port,
                    'packet_count': count,
                    'percentage': percentage
                }

        # 如果没有找到UDP数据包，显示最频繁的以太网通信
        if self.ethernet_stats:
            print(f"\n💡 建议检查以太网通信:")
            top_ethernet = sorted(self.ethernet_stats.items(), key=lambda x: x[1], reverse=True)[0]
            (src_mac, dst_mac), count = top_ethernet
            percentage = (count / self.total_packets) * 100
            print(f"   最频繁通信: {src_mac} -> {dst_mac}")
            print(f"   数据包数: {count} ({percentage:.1f}%)")
            print(f"   这可能是激光雷达的MAC地址通信")

        print(f"\n⚠️  未检测到明显的IP/UDP激光雷达数据流")
        print(f"   激光雷达可能使用自定义协议或二层通信")
        return None

    def get_protocol_name(self, eth_type):
        """
        获取协议名称
        """
        protocol_map = {
            0x0800: "IPv4",
            0x0806: "ARP",
            0x86DD: "IPv6",
            0x8100: "VLAN",
            0x88CC: "LLDP",
            0x8847: "MPLS",
            0x8848: "MPLS",
        }
        return protocol_map.get(eth_type, f"Unknown(0x{eth_type:04X})")


def signal_handler(sig, frame):
    """
    信号处理函数，用于优雅退出
    """
    print('\n\n程序被用户中断')
    sys.exit(0)


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='激光雷达网络参数自动检测工具')
    # 移除接口参数，固定使用以太网
    # parser.add_argument('-i', '--interface', type=str, default=None,
    #                     help='指定网络接口名称（默认监听所有接口）')
    parser.add_argument('-t', '--time', type=int, default=3,
                        help='监听时长（秒），默认10秒')
    parser.add_argument('-m', '--min-packets', type=int, default=5,
                        help='最小数据包数量阈值，默认5个')
    
    args = parser.parse_args()
    
    # 注册信号处理函数
    signal.signal(signal.SIGINT, signal_handler)
    
    # 创建检测器 - 固定使用以太网接口
    detector = LidarNetworkDetector(
        interface="以太网",  # 固定指定为以太网接口
        duration=args.time,
        min_packets=args.min_packets
    )
    
    # 开始检测
    result = detector.detect_lidar_params()
    
    if result:
        print(f"\n✅ 检测成功！建议的激光雷达配置:")
        print(f"   目标IP: {result['target_ip']}")
        print(f"   数据端口: {result['data_port']}")
        print(f"   设备端口: {result['device_port']}")
    else:
        print(f"\n❌ 检测失败，请检查激光雷达连接状态")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    main()
