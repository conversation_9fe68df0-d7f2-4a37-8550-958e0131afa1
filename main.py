#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from collections import defaultdict
from scapy.all import sniff, UDP, IP
import argparse


class LidarNetworkDetector:
    def __init__(self, interface="以太网", duration=2, min_packets=5):
        """
        初始化激光雷达网络检测器
        """
        self.interface = interface
        self.duration = duration
        self.min_packets = min_packets
        self.udp_stats = defaultdict(int)  # 存储UDP数据包统计
        self.total_packets = 0

    def packet_handler(self, packet):
        """
        数据包处理函数 - 专注于UDP数据包检测
        """
        try:
            self.total_packets += 1

            # 只关注UDP数据包（激光雷达常用协议）
            if packet.haslayer(IP) and packet.haslayer(UDP):
                src_ip = packet[IP].src
                src_port = packet[UDP].sport  # 激光雷达的发送端口
                dst_ip = packet[IP].dst
                dst_port = packet[UDP].dport
                key = f"{src_ip}:{src_port}->{dst_ip}:{dst_port}"
                self.udp_stats[key] += 1

        except Exception:
            pass

    def detect_lidar_params(self):
        """
        检测激光雷达网络参数
        """

        try:
            sniff(
                iface=self.interface,
                prn=self.packet_handler,
                timeout=self.duration,
                store=False
            )
        except KeyboardInterrupt:
            print("\n用户中断")
        except Exception as e:
            print(f"\n错误: {e}")

        return self.analyze_results()

    def analyze_results(self):
        """
        分析监听结果
        """
        if not self.udp_stats:
            print("未检测到UDP数据包")
            return None

        # 找出最频繁的UDP通信
        sorted_stats = sorted(self.udp_stats.items(), key=lambda x: x[1], reverse=True)

        if sorted_stats:
            top_communication, count = sorted_stats[0]
            if count >= self.min_packets:
                # 解析: src_ip:src_port->dst_ip:dst_port
                src_part, dst_part = top_communication.split('->')
                src_ip, src_port = src_part.split(':')
                dst_ip, dst_port = dst_part.split(':')

                src_port = int(src_port)
                dst_port = int(dst_port)

                return {
                    'lidar_ip': src_ip,
                    'lidar_port': src_port,
                    'target_ip': dst_ip,
                    'target_port': dst_port,
                    'packet_count': count
                }

        print("未找到足够的数据包")
        return None


def main():
    parser = argparse.ArgumentParser(description='激光雷达网络参数检测工具')
    parser.add_argument('-t', '--time', type=int, default=0.5,
                        help='监听时长（秒）')
    parser.add_argument('-m', '--min-packets', type=int, default=5,
                        help='最小数据包阈值')

    args = parser.parse_args()

    detector = LidarNetworkDetector(
        duration=args.time,
        min_packets=args.min_packets
    )

    result = detector.detect_lidar_params()

    if result:
        print(f"\n检测成功:")
        print(f"目标IP: {result['target_ip']}")
        print(f"数据端口: {result['target_port']}")
        print(f"设备端口: {result['lidar_port']}")
    else:
        print(f"\n检测失败")


if __name__ == "__main__":
    main()
