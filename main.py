#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激光雷达网络参数自动检测工具
自动监听以太网接口，统计最频繁出现的目标IP和端口号
"""

from collections import defaultdict
from scapy.all import sniff, UDP, IP
import argparse


class LidarNetworkDetector:
    def __init__(self, interface="以太网", duration=10, min_packets=5):
        """
        初始化激光雷达网络检测器
        """
        self.interface = interface
        self.duration = duration
        self.min_packets = min_packets
        self.udp_stats = defaultdict(int)  # 存储UDP数据包统计
        self.total_packets = 0

    def packet_handler(self, packet):
        """
        数据包处理函数 - 专注于UDP数据包检测
        """
        try:
            self.total_packets += 1

            # 只关注UDP数据包（激光雷达常用协议）
            if packet.haslayer(IP) and packet.haslayer(UDP):
                dst_ip = packet[IP].dst
                dst_port = packet[UDP].dport
                key = f"{dst_ip}:{dst_port}"
                self.udp_stats[key] += 1

        except Exception:
            pass

    def detect_lidar_params(self):
        """
        检测激光雷达网络参数
        """
        print("激光雷达网络参数检测工具")
        print(f"监听接口: {self.interface}, 时长: {self.duration}秒")
        print("开始监听...")

        try:
            sniff(
                iface=self.interface,
                prn=self.packet_handler,
                timeout=self.duration,
                store=False
            )
        except KeyboardInterrupt:
            print("\n用户中断")
        except Exception as e:
            print(f"\n错误: {e}")

        print("分析结果...")
        return self.analyze_results()

    def analyze_results(self):
        """
        分析监听结果
        """
        if not self.udp_stats:
            print("未检测到UDP数据包")
            return None

        # 找出最频繁的UDP目标
        sorted_stats = sorted(self.udp_stats.items(), key=lambda x: x[1], reverse=True)

        if sorted_stats:
            top_target, count = sorted_stats[0]
            if count >= self.min_packets:
                ip, port = top_target.split(':')
                port = int(port)

                print(f"\n检测结果:")
                print(f"目标IP: {ip}")
                print(f"数据端口: {port}")
                print(f"设备端口: {port + 1} (推测)")
                print(f"数据包数: {count}")

                return {
                    'target_ip': ip,
                    'data_port': port,
                    'device_port': port + 1,
                    'packet_count': count
                }

        print("未找到足够的数据包")
        return None


def main():
    parser = argparse.ArgumentParser(description='激光雷达网络参数检测工具')
    parser.add_argument('-t', '--time', type=int, default=10,
                        help='监听时长（秒）')
    parser.add_argument('-m', '--min-packets', type=int, default=5,
                        help='最小数据包阈值')

    args = parser.parse_args()

    detector = LidarNetworkDetector(
        duration=args.time,
        min_packets=args.min_packets
    )

    result = detector.detect_lidar_params()

    if result:
        print(f"\n检测成功:")
        print(f"目标IP: {result['target_ip']}")
        print(f"数据端口: {result['data_port']}")
        print(f"设备端口: {result['device_port']}")
    else:
        print(f"\n检测失败")


if __name__ == "__main__":
    main()
